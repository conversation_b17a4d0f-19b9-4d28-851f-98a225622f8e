<cfcomponent extends="model.admin.admin" output="no">
	<cfset defaultEvent = 'controller'>
	<cfset variables.instanceSettings = structNew()>

	<cffunction name="controller" access="public" output="false" returntype="struct" hint="controller for this app">
		<cfargument name="Event" type="any">
		
		<cfscript>
			var local = structNew();
			
			//set rights into event
			local.tmpRights = buildRightAssignments(siteResourceID=this.siteResourceID, memberID=session.cfcuser.memberdata.memberID, siteID=arguments.event.getValue('mc_siteInfo.siteid'));
			arguments.event.getCollection()['mc_admintoolInfo']['myRights'] = local.tmpRights;

			this.appInstanceSettings = super.getInstanceSettings(this.appInstanceID);
			local.appInstanceSettings = super.getInstanceSettings(this.appInstanceID);
			
			this.objMFS = CreateObject("component","model.system.platform.memberFieldsets");
		</cfscript>
		<cfset variables.supportedSocialIcons = "facebook,linkedin,twitter,500px,deliciou,digg,rss,path,google+,vimeo,youtube,wordpress,spotify,blogger,tumblr,blog">
		
		<cfset local.methodToRun = this[arguments.event.getValue('mca_ta')]>
		<cfreturn local.methodToRun(arguments.event)>
	</cffunction>

	<cffunction name="matchToolView" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">

		<cfscript>
			var local = structNew();
			
			this.link.generateMatch = buildCurrentLink(arguments.event,"generateMatch");	
			this.link.editMemberLink = buildLinkToTool(toolType='MemberAdmin',mca_ta='edit');
			this.link.relationshipLink = buildLinkToTool(toolType='MemberHistoryAdmin',mca_ta='listRelationships');

			this.link.showMatchLink	= "#arguments.event.getValue('mc_adminNav.adminHomeResource')#&mca_jsonlib=mcdatatable&com=networkGeneratorJSON&meth=getMatchedUsers&mode=stream";					
			this.link.showUnMatchLink = "#arguments.event.getValue('mc_adminNav.adminHomeResource')#&mca_jsonlib=mcdatatable&com=networkGeneratorJSON&meth=getUnMatchedUsers&mode=stream";
		</cfscript>
		
		<cfsavecontent variable="local.data">
			<cfinclude template="frm_matchTools.cfm">
		</cfsavecontent>
		
		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="matchSettingsView" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">
		
		<cfscript>
			var local = structNew();
			local.networkAdminSiteResourceID = application.objSiteResource.getSiteResourceIDForResourceType(resourceTypeName='networkGeneratorAdmin',siteID=arguments.event.getValue('mc_siteinfo.siteid'));
			local.link.editClassification	= buildCurrentLink(arguments.event,"editClassification") & "&mode=direct";
			local.link.classificationList = "#arguments.event.getValue('mc_adminNav.adminHomeResource')#&mca_jsonlib=mcdatatable&com=networkGeneratorJSON&meth=getClassifications&mode=stream";

			local.siteResourceID = this.siteResourceID;

			local.strParticipantFieldsSelector = createObject("component","model.admin.common.modules.fieldSetSelector.fieldSetSelector").getMultipleFieldSetSelector(
				selectorID="fsSelectorForParticipantFS",
				getFieldSetDataFunc="getAvailableAndSelectedFieldSetsForParticipant",
				addFieldSetUsageFunc="createMemberFieldUsageForParticipant",
				removeFieldSetUsageFunc="removeParticipantFieldUsage",
				hasPermissionAction=false,
				hasOrderingAction=false);
		</cfscript>
		
		<cfsavecontent variable="local.data">
			<cfinclude template="dsp_matchSettings.cfm">
		</cfsavecontent>
		
		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>
	
	<cffunction name="editClassification" access="public" output="false" returntype="struct" hint="Add Sub-Panel page">
		<cfargument name="Event" type="any" />
		<cfscript>
			var local = structNew();
			local.rc = arguments.event.getCollection();	

			local.classificationID = arguments.event.getValue('classificationID',0);
			local.qryGetClassification = getClassificationByID(local.classificationID);
			local.nameOverride = local.qryGetClassification.name;
			local.siteResourceID = arguments.event.getValue('siteResourceID',0);
			local.allowSearch = local.qryGetClassification.allowSearch;
			local.showInSearchResults = local.qryGetClassification.showInSearchResults;
			local.groupSetID = local.qryGetClassification.groupSetID;	
			local.link.saveClassification	= buildCurrentLink(arguments.event,"saveClassification") & "&mode=stream";		
			local.qryGroupSets = CreateObject("component","model.admin.MemberGroupSets.MemberGroupSets").getGroupSets(orgID=arguments.event.getValue('mc_siteInfo.orgID'));
		</cfscript>
	
		<cfsavecontent variable="local.data">
			<cfoutput>
				<cfinclude template="frm_classification.cfm" />
			</cfoutput>
		</cfsavecontent>
		<cfreturn returnAppStruct(local.data,"echo") />
	</cffunction>

	<cffunction name="saveClassification" access="public" returntype="struct">
		<cfargument name="Event" type="any" />

		<cfscript>
			var local = structNew();
			local.data = "";
			
			local.classificationID = arguments.event.getValue('classificationID');
		</cfscript>
		
		<cfif local.classificationID>
			<cfquery name="local.updateClassification" datasource="#application.dsn.membercentral.dsn#">
				update
					dbo.ams_classifications
				set
					<cfif arguments.event.valueExists('nameOverride') and len(trim(arguments.event.getValue('nameOverride')))>
						name = <cfqueryparam value="#arguments.event.getValue('nameOverride')#" cfsqltype="cf_sql_varchar" />,
					<cfelse>
						name = NULL,
					</cfif>
					<cfif arguments.event.valueExists('allowSearch') and val(arguments.event.getValue('allowSearch'))>
						allowSearch = 1,
					<cfelse>
						allowSearch = 0,
					</cfif>
					<cfif arguments.event.valueExists('showInSearchResults') and val(arguments.event.getValue('showInSearchResults'))>
						showInSearchResults = 1,
					<cfelse>
						showInSearchResults = 0,
					</cfif>
					<cfif arguments.event.valueExists('groupSetID') and val(arguments.event.getValue('groupSetID'))>
						groupSetID = <cfqueryparam value="#arguments.event.getValue('groupSetID')#" cfsqltype="cf_sql_integer" />
					<cfelse>
						groupSetID = NULL
					</cfif>
				where
					classificationID = <cfqueryparam value="#arguments.event.getValue('classificationID')#" cfsqltype="cf_sql_integer" />
			</cfquery>
		<cfelse>
			<cfquery name="local.updateClassification" datasource="#application.dsn.membercentral.dsn#">
				set nocount on
				declare @newOrderItem int
				
				select 
					@newOrderItem = case
										when max(classificationOrder) is null then 1
										else max(classificationOrder) + 1 
									end
				from 
					dbo.ams_classifications
				where 
					siteResourceID = <cfqueryparam value="#arguments.event.getValue('siteResourceID')#" cfsqltype="cf_sql_integer" />
				
				insert into dbo.ams_classifications(
					name,
					allowSearch,
					siteResourceID,
					area,
					showInSearchResults,
					groupSetID,
					classificationOrder 
				)
				values (
					<cfif arguments.event.valueExists('nameOverride') and len(trim(arguments.event.getValue('nameOverride')))>
						<cfqueryparam value="#arguments.event.getValue('nameOverride')#" cfsqltype="cf_sql_varchar" />,
					<cfelse>
						NULL,
					</cfif>
					<cfqueryparam value="#arguments.event.getValue('allowSearch',0)#" cfsqltype="cf_sql_bit" />,
					<cfqueryparam value="#arguments.event.getValue('siteResourceID')#" cfsqltype="cf_sql_integer" />,
					'details',
					<cfqueryparam value="#arguments.event.getValue('showInSearchResults',0)#" cfsqltype="cf_sql_bit" />,
					<cfqueryparam value="#arguments.event.getValue('groupSetID')#" cfsqltype="cf_sql_integer" />,
					@newOrderItem
				)
				set nocount off
			</cfquery>
		</cfif>
		
		<cfsavecontent variable="local.data">
			<cfoutput>
			<script language="javascript">
				top.refreshGrids();
				top.MCModalUtils.hideModal();
			</script>
			</cfoutput>
		</cfsavecontent>
	
		<cfreturn returnAppStruct(local.data,"echo") />
	</cffunction>

	<cffunction name="getClassificationByID" access="public" output="false" returntype="query">
		<cfargument name="classificationid" type="numeric" required="true" />
		
		<cfset var local = structNew() />
		
		<cfquery name="local.qryGetClassification" datasource="#application.dsn.membercentral.dsn#">
			select 
				c.classificationid,
				c.name,
				mgs.groupSetName,
				c.allowSearch,
				c.siteResourceID,
				c.showInSearchResults,
				c.groupSetID
			from 
				ams_classifications c
				inner join dbo.ams_memberGroupSets mgs on
					mgs.groupSetID = c.groupSetID
			where
				c.classificationid = <cfqueryparam value="#arguments.classificationid#" cfsqltype="cf_sql_integer" />
		</cfquery>

		<cfreturn local.qryGetClassification />
	</cffunction>

	<cffunction name="getClassifications" access="public" output="false" returntype="query">
		<cfargument name="siteResourceID" type="numeric" required="true">
		
		<cfset var qryClassifications = "">
		
		<cfquery name="qryClassifications" datasource="#application.dsn.membercentral.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;
		
			select c.classificationid, c.name, mgs.groupSetName, c.allowSearch, c.siteResourceID, c.showInSearchResults, c.groupSetID, c.classificationOrder 
			from dbo.ams_classifications c
			inner join dbo.ams_memberGroupSets mgs on mgs.groupSetID = c.groupSetID
			where c.siteResourceID = <cfqueryparam value="#arguments.siteResourceID#" cfsqltype="CF_SQL_INTEGER">
			order by c.classificationOrder;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>
		
		<cfreturn qryClassifications>
	</cffunction>
	
	<cffunction name="getShowMatch" access="public" output="false" returntype="query">
		<cfargument name="Event" type="any" required="true">
		
		<cfset var local = structNew()>
		<cfset local.typeID = 2>
		<cfset local.fromDate = arguments.event.getTrimValue('fDate','')>
		<cfif len(local.fromDate)>
			<cfset local.fromDate = createDateTime(year(local.fromDate), month(local.fromDate), 1, 00, 00, 00)>
		</cfif>
		<cfset local.toDate = arguments.event.getTrimValue('fDate','')>
		<cfif len(local.toDate)>
			<cfset local.toDate = createDateTime(year(local.toDate), month(local.toDate), daysInMonth(local.toDate), 23, 59, 59.997)>
		</cfif>
	
		<cfset local.RelationshipAdminSRID = application.objSiteResource.getSiteResourceIDForResourceType(resourceTypeName='RelationshipAdmin',siteID=arguments.event.getValue('mc_siteInfo.siteid'))>

		<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryMemHistory" result="local.qryMemHistoryResult">
			SET XACT_ABORT, NOCOUNT ON;
			BEGIN TRY
				SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

				DECLARE @siteID int,@orgID int, @categoryID int, @subCategoryID int, @userDateFrom date, @userEndDateTo datetime, 
						 @enteredByMemberID int, @typeID int, @currentMemberID int,@relationshipAdmin_siteResourceID int,@catTreeID int;
						 
				SELECT @orgID = orgID from dbo.organizations where orgcode = 'INDY';
				SELECT @siteID = siteID from dbo.sites where orgID = @orgID;
				SET @relationshipAdmin_siteResourceID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.RelationshipAdminSRID#">;
				SELECT @catTreeID = dbo.fn_getCategoryTreeIDForSiteResourceID(@relationshipAdmin_siteResourceID);
				select @categoryID = categoryID from dbo.cms_categories where categoryTreeID = @catTreeID and categoryCode = 'Network';
				select @subCategoryID = categoryID from dbo.cms_categories where categoryTreeID = @catTreeID and categoryCode = 'Matched';				
				
				SET @typeID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.typeID#">;
				SET @currentMemberID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#session.cfcuser.memberdata.memberid#">;
				
				<cfif len(local.fromDate)>
					SET @userDateFrom = <cfqueryparam cfsqltype="CF_SQL_DATE" value="#local.fromDate#">;
				</cfif>
				<cfif len(local.toDate)>
					SET @userEndDateTo = <cfqueryparam cfsqltype="CF_SQL_TIMESTAMP" value="#local.toDate#">;
				</cfif>

				IF OBJECT_ID('tempdb..##tblMemHistorySearch') IS NOT NULL 
					DROP TABLE ##tblMemHistorySearch;
				CREATE TABLE ##tblMemHistorySearch (historyID int PRIMARY KEY);

				INSERT INTO ##tblMemHistorySearch
				select mh.historyID
				from dbo.ams_memberHistory as mh
				inner join dbo.cms_categories as cP on cP.categoryID = mh.categoryID
				inner join dbo.cms_categoryTrees as cPt on cPt.categoryTreeID = cP.categoryTreeID
				inner join dbo.ams_members as m on m.memberID = mh.memberID and m.orgID = @orgID
				inner join dbo.ams_members as mActive on mActive.memberID = m.activeMemberID and mActive.orgID = @orgID
				inner join dbo.ams_members as mEntered on mEntered.memberID = mh.enteredByMemberID and mEntered.orgID in (@orgID,1)
				inner join dbo.ams_members as mEnteredActive on mEnteredActive.memberID = mEntered.activeMemberID and mEnteredActive.orgID in (@orgID,1)
				left outer join dbo.cms_categories cg2 on cg2.categoryID = mh.subCategoryID
				left outer join dbo.ams_members as mLink 
					inner join dbo.ams_members as mLinkActive on mLinkActive.memberID = mLink.activeMemberID and mLinkActive.orgID = @orgID
					on mLink.memberID = mh.linkMemberID and mLink.orgID = @orgID
				where cPt.siteID = @siteID
				and mh.typeID = @typeID
				and mh.siteID = @siteID
				and mh.status = 'A'
				and mh.categoryID = @categoryID
				and mh.subCategoryID = @subCategoryID				
				<cfif len(local.fromDate)>
					and mh.userDate >= @userDateFrom
				</cfif>
				<cfif len(local.toDate)>
					and mh.userEndDate <= @userEndDateTo
				</cfif>;
				
				DECLARE @posStart int, @posStartAndCount int, @totalCount int;
				
				<cfif arguments.event.getTrimValue('posStart','') NEQ ''>
					SET @posStart = <cfqueryparam value="#arguments.event.getValue('posStart')#" cfsqltype="CF_SQL_INTEGER">
				</cfif>
				<cfif arguments.event.getTrimValue('count','') NEQ ''>
					SET @posStartAndCount = @posStart + <cfqueryparam value="#arguments.event.getValue('count')#" cfsqltype="CF_SQL_INTEGER">;						
				</cfif>

				IF OBJECT_ID('tempdb..##tblMemberHistory') IS NOT NULL 
					DROP TABLE ##tblMemberHistory;
				CREATE TABLE ##tblMemberHistory (historyID int, userDate datetime, userEndDate datetime, quantity int, dollarAmt decimal(14,2), description varchar(max), 
					dateEntered datetime, memberID int, memberName varchar(300), memberCompany varchar(200), enteredByMemberName varchar(300), linkMemberID int, 
					linkMemberName varchar(300), linkMemberCompany varchar(200), typeName varchar(200), categoryName varchar(200), catSiteResourceID int, row int);

				INSERT INTO ##tblMemberHistory
				select historyID, userDate, userEndDate, quantity, dollarAmt, description, dateEntered, memberID, memberName, memberCompany, 
					EnteredByMemberName, linkMemberID, linkMemberName, linkMemberCompany, typeName, categoryName, siteResourceID,
					ROW_NUMBER() OVER (ORDER BY dateEntered desc, userDate desc, historyID desc) as row
				from (
					select mh.historyID, mh.userDate, mh.userEndDate, mh.quantity, mh.dollarAmt, mh.description, mh.dateEntered,
						mActive.memberID, mActive.lastName + ', ' + mActive.firstName + ' (' + mActive.memberNumber + ')' as memberName, mActive.company as memberCompany, 
						mEnteredActive.lastName + ', ' + mEnteredActive.firstName + ' (' + mEnteredActive.memberNumber + ')' as EnteredByMemberName, mLinkActive.memberID as linkMemberID, 
						mLinkActive.lastName + ', ' + mLinkActive.firstName + ' (' + mLinkActive.memberNumber + ')' as linkMemberName, mLinkActive.company as linkMemberCompany, 
						cP.categoryName as typeName, cg2.categoryName as categoryName, cP.siteResourceID
					from ##tblMemHistorySearch as tmp
					inner join dbo.ams_memberHistory as mh on mh.historyID = tmp.historyID
						and mh.siteID = @siteID
					inner join dbo.cms_categories as cP on cP.categoryID = mh.categoryID
					inner join dbo.cms_categoryTrees as cPt on cPt.categoryTreeID = cP.categoryTreeID
					inner join dbo.ams_members as m on m.memberID = mh.memberID and m.orgID = @orgID
					inner join dbo.ams_members as mActive on mActive.memberID = m.activeMemberID and mActive.orgID = @orgID
					inner join dbo.ams_members as mEntered on mEntered.memberID = mh.enteredByMemberID and mEntered.orgID in (@orgID,1)
					inner join dbo.ams_members as mEnteredActive on mEnteredActive.memberID = mEntered.activeMemberID and mEnteredActive.orgID in (@orgID,1)
					left outer join dbo.cms_categories cg2 on cg2.categoryID = mh.subCategoryID
					left outer join dbo.ams_members as mLink 
						inner join dbo.ams_members as mLinkActive on mLinkActive.memberID = mLink.activeMemberID and mLinkActive.orgID = @orgID
						on mLink.memberID = mh.linkMemberID and mLink.orgID = @orgID
				) tmpOuter;

				select @totalCount = @@ROWCOUNT;

				select *, @totalCount as totalCount
				from ##tblMemberHistory
				where row > @posStart
				and row <= @posStartAndCount
				order by row;

				IF OBJECT_ID('tempdb..##tblMemberHistory') IS NOT NULL 
					DROP TABLE ##tblMemberHistory;
				IF OBJECT_ID('tempdb..##tblMemHistorySearch') IS NOT NULL 
					DROP TABLE ##tblMemHistorySearch;

				SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
			END TRY
			BEGIN CATCH
				IF @@trancount > 0 ROLLBACK TRANSACTION;
				SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
				EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
			END CATCH			
		</cfquery>
		
		<cfreturn local.qryMemHistory>
	</cffunction>
	
	<cffunction name="getShowUnMatch" access="public" output="false" returntype="query">
		<cfargument name="Event" type="any" required="true">
		
		<cfset var local = structNew()>
		<cfset local.typeID = 2>
		<cfset local.networkAdminSiteResourceID = application.objSiteResource.getSiteResourceIDForResourceType(resourceTypeName='networkGeneratorAdmin',siteID=arguments.event.getValue('mc_siteinfo.siteid'))>
		
		<cfset local.fromDate = arguments.event.getTrimValue('fDate','')>
		<cfif len(local.fromDate)>
			<cfset local.fromDate = createDateTime(year(local.fromDate), month(local.fromDate), 1, 00, 00, 00)>
		</cfif>
		<cfset local.toDate = arguments.event.getTrimValue('fDate','')>
		<cfif len(local.toDate)>
			<cfset local.toDate = createDateTime(year(local.toDate), month(local.toDate), daysInMonth(local.toDate), 23, 59, 59.997)>
		</cfif>
			
		<cfset local.RelationshipAdminSRID = application.objSiteResource.getSiteResourceIDForResourceType(resourceTypeName='RelationshipAdmin',siteID=arguments.event.getValue('mc_siteInfo.siteid'))>
		<cfset local.objMemberFieldSet = CreateObject("component","model.admin.MemberFieldSets.MemberFieldSets")>
		<cfset local.fieldSets = local.objMemberFieldSet.getUsageDetails(local.networkAdminSiteResourceID)>
		
		<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryMemHistory" result="local.qryMemHistoryResult">
			SET XACT_ABORT, NOCOUNT ON;
			BEGIN TRY
				SET TRANSACTION ISOLATION LEVEL SNAPSHOT;			

				DECLARE @orgID int, @siteID int, @categoryID int, @subCategoryID int, @userDateFrom date, @userEndDateTo datetime,
					@enteredByMemberID int, @typeID int, @relationshipAdmin_siteResourceID int, @catTreeID int, @groupID int, 
					@dateFromStart datetime, @dateFromEnd datetime, @dateToStart datetime, @dateToEnd datetime, @posStart int,
					@posStartAndCount int, @totalCount int, @outputFieldsXML xml;
				SET @posStart = <cfqueryparam value="#arguments.event.getValue('posStart')#" cfsqltype="CF_SQL_INTEGER">;
				SET @posStartAndCount = @posStart + <cfqueryparam value="#arguments.event.getValue('count')#" cfsqltype="CF_SQL_INTEGER">;

				DECLARE @previousMatchCategories TABLE (categoryID int PRIMARY KEY);
				IF OBJECT_ID('tempdb..##tblMemHistorySearch') IS NOT NULL
					DROP TABLE ##tblMemHistorySearch;
				IF OBJECT_ID('tempdb..##tblMemUnmatch') IS NOT NULL
					DROP TABLE ##tblMemUnmatch;
				IF OBJECT_ID('tempdb..##tblMemUnmatchLinked') IS NOT NULL
					DROP TABLE ##tblMemUnmatchLinked;				
				IF OBJECT_ID('tempdb..##tmpMembers') IS NOT NULL 
					DROP TABLE ##tmpMembers;
				IF OBJECT_ID('tempdb..##tmpMembersFS') IS NOT NULL
					DROP TABLE ##tmpMembersFS;
				CREATE TABLE ##tblMemHistorySearch (historyID int PRIMARY KEY);				
				CREATE TABLE ##tblMemUnmatchLinked (linkmemberid int);
				CREATE TABLE ##tblMemUnmatch (memberid int);
				CREATE TABLE ##tmpMembers (memberid int PRIMARY KEY,firstName varchar(75), middleName varchar(25), lastName varchar(75), memberNumber varchar(50), company varchar(200), prefix varchar(50), suffix varchar(50), professionalSuffix varchar(100), row int);
				CREATE TABLE ##tmpMembersFS (MFSAutoID int IDENTITY(1,1) NOT NULL);
				
				SET @orgID = dbo.fn_getOrgIDFromOrgCode('INDY');
				SET @siteID = dbo.fn_getSiteIDFromSiteCode('INDY');

				SET @relationshipAdmin_siteResourceID = <cfqueryparam value="#local.RelationshipAdminSRID#" cfsqltype="CF_SQL_INTEGER">;
				SELECT @catTreeID = dbo.fn_getCategoryTreeIDForSiteResourceID(@relationshipAdmin_siteResourceID);

				SELECT @categoryID = categoryID
				FROM dbo.cms_categories
				WHERE categoryTreeID = @catTreeID
				AND categoryCode = 'Network';

				SELECT @subCategoryID = categoryID
				FROM dbo.cms_categories
				WHERE categoryTreeID = @catTreeID
				AND categoryCode = 'Matched';

				SET @typeID = 2;

				-- load in the relationship categories that signify previous matches or dissallowed matches
				insert into @previousMatchCategories
				select categoryID
				from membercentral.dbo.cms_categories
				where categoryTreeID = @catTreeID
				and categoryCode = 'Network';

				insert into @previousMatchCategories
				select c.categoryID
				from membercentral.dbo.cms_categories c
				inner join @previousMatchCategories pm on pm.categoryID = c.parentCategoryID;

				<cfif len(local.fromDate)>
					SET @userDateFrom = <cfqueryparam cfsqltype="CF_SQL_DATE" value="#local.fromDate#">;
					--drop time and recalculate
					set @dateFromStart = CAST(@userDateFrom AS DATE);
					set @dateFromEnd = dateadd(millisecond,-1,dateadd(day,1,@dateFromStart));

				</cfif>
				<cfif len(local.toDate)>
					SET @userEndDateTo = <cfqueryparam cfsqltype="CF_SQL_TIMESTAMP" value="#local.toDate#">;
					--drop time and recalculate
					set @dateToStart = CAST(@userEndDateTo AS DATE);
					set @dateToEnd = dateadd(millisecond,-1,dateadd(day,1,@dateToStart));
				</cfif>

				INSERT INTO ##tblMemHistorySearch
				SELECT mh.historyID
				FROM dbo.ams_memberHistory AS mh
				INNER JOIN @previousMatchCategories c on c.categoryID = mh.categoryID
					and mh.status = 'A'
					and mh.siteID = @siteID
				INNER JOIN dbo.ams_members AS m ON m.memberID = mh.memberID and m.orgID = @orgID
				INNER JOIN dbo.ams_members AS mActive ON mActive.memberID = m.activeMemberID
					and mActive.status = 'A' and mActive.orgID = @orgID
				INNER JOIN dbo.ams_members AS mEntered ON mEntered.memberID = mh.enteredByMemberID and mEntered.orgID in (@orgID,1)
				INNER JOIN dbo.ams_members AS mEnteredActive ON mEnteredActive.memberID = mEntered.activeMemberID and mEnteredActive.orgID in (@orgID,1)
				LEFT OUTER JOIN dbo.cms_categories cg2 ON cg2.categoryID = mh.subCategoryID
				LEFT OUTER JOIN dbo.ams_members AS mLink
					INNER JOIN dbo.ams_members AS mLinkActive ON mLinkActive.memberID = mLink.activeMemberID and mLinkActive.orgID = @orgID
					ON mLink.memberID = mh.linkMemberID and mLink.orgID = @orgID;
					
				
				IF OBJECT_ID('tempdb..##tblMemberHistory') IS NOT NULL
					DROP TABLE ##tblMemberHistory;
				CREATE TABLE ##tblMemberHistory (
					historyID int,
					userDate datetime,
					userEndDate datetime,
					quantity int,
					dollarAmt decimal(14, 2),
					description varchar(max),
					dateEntered datetime,
					memberID int,
					memberName varchar(300),
					memberCompany varchar(200),
					enteredByMemberName varchar(300),
					linkMemberID int,
					linkMemberName varchar(300),
					linkMemberCompany varchar(200),
					typeName varchar(200),
					categoryName varchar(200),
					catSiteResourceID int,
					row int
				);

				INSERT INTO ##tblMemberHistory
				SELECT
					historyID,
					userDate,
					userEndDate,
					quantity,
					dollarAmt,
					description,
					dateEntered,
					memberID,
					memberName,
					memberCompany,
					EnteredByMemberName,
					linkMemberID,
					linkMemberName,
					linkMemberCompany,
					typeName,
					categoryName,
					siteResourceID,
					ROW_NUMBER() OVER (ORDER BY dateEntered DESC, userDate DESC, historyID DESC) AS row
				FROM (SELECT
					mh.historyID,
					mh.userDate,
					mh.userEndDate,
					mh.quantity,
					mh.dollarAmt,
					mh.description,
					mh.dateEntered,
					mActive.memberID,
					mActive.lastName + ', ' + mActive.firstName + ' (' + mActive.memberNumber + ')' AS memberName,
					mActive.company AS memberCompany,
					mEnteredActive.lastName + ', ' + mEnteredActive.firstName + ' (' + mEnteredActive.memberNumber + ')' AS EnteredByMemberName,
					mLinkActive.memberID AS linkMemberID,
					mLinkActive.lastName + ', ' + mLinkActive.firstName + ' (' + mLinkActive.memberNumber + ')' AS linkMemberName,
					mLinkActive.company AS linkMemberCompany,
					cP.categoryName AS typeName,
					cg2.categoryName AS categoryName,
					cP.siteResourceID
				FROM ##tblMemHistorySearch AS tmp
				INNER JOIN dbo.ams_memberHistory AS mh ON mh.historyID = tmp.historyID
					AND mh.siteID = @siteID
				INNER JOIN dbo.cms_categories AS cP ON cP.categoryID = mh.categoryID
				INNER JOIN dbo.cms_categoryTrees AS cPt ON cPt.categoryTreeID = cP.categoryTreeID
				INNER JOIN dbo.ams_members AS m ON m.memberID = mh.memberID
				INNER JOIN dbo.ams_members AS mActive ON mActive.memberID = m.activeMemberID
				INNER JOIN dbo.ams_members AS mEntered ON mEntered.memberID = mh.enteredByMemberID
				INNER JOIN dbo.ams_members AS mEnteredActive ON mEnteredActive.memberID = mEntered.activeMemberID
				LEFT OUTER JOIN dbo.cms_categories cg2 ON cg2.categoryID = mh.subCategoryID
				LEFT OUTER JOIN dbo.ams_members AS mLink
					INNER JOIN dbo.ams_members AS mLinkActive ON mLinkActive.memberID = mLink.activeMemberID
					ON mLink.memberID = mh.linkMemberID where 1 = 1 
				<cfif len(local.fromDate)>
					and mh.userDate >= @userDateFrom
				</cfif>
				<cfif len(local.toDate)>
					and mh.userEndDate <= @userEndDateTo
				</cfif>

				) tmpOuter;
				
				SELECT @groupID = groupID 
				from dbo.ams_Groups 
				where orgID = @orgID 
					and uid = '0117543c-8e0e-4ea3-bd8c-970159416f3a';

				INSERT INTO ##tblMemUnmatch	
				SELECT memberid
				FROM (	select distinct m.memberid
						from dbo.cache_members_groups as mg 
							INNER JOIN dbo.ams_members as m on m.memberid = mg.memberid and m.memberid = m.activememberID and m.status = 'A'
							INNER JOIN dbo.ams_groups as g on g.groupID = mg.groupID 
							left outer join dbo.ams_memberHistory as mh on mh.memberID = m.memberID	
								and mh.siteID = @siteID
						where m.orgID = @orgID 
							and mg.groupid = @groupID 
							and m.status <> 'D') 
				AS tempMemUnmatch;

				INSERT INTO ##tmpMembers (memberid, [row])
				select t1.memberid, ROW_NUMBER() OVER (ORDER BY t2.dateEntered desc, t2.userDate desc, t2.historyID desc, t1.memberid desc) as row
				from ##tblMemUnmatch t1 
					left outer join ##tblMemberHistory t2 ON t2.memberID = t1.memberid
					left outer join ##tblMemberHistory t3 ON t3.linkMemberID = t1.memberid
				where t2.linkMemberID is null and t3.linkMemberID is null;

				SET @totalCount = @@ROWCOUNT;
				
				DELETE FROM ##tmpMembers WHERE NOT (row > @posStart AND row <= @posStartAndCount);
				
				UPDATE tmpM
				SET tmpM.firstname = m.firstname,
					tmpM.middlename = m.middlename,
					tmpM.lastname = m.lastname,
					tmpM.memberNumber = m.memberNumber,
					tmpM.company = m.company,
					tmpM.prefix = m.prefix,
					tmpM.suffix = m.suffix,
					tmpM.professionalsuffix = m.professionalsuffix
				FROM ##tmpMembers tmpM
				INNER JOIN dbo.ams_members AS m ON m.memberid = tmpM.memberid
				WHERE m.memberid = m.activememberID AND m.status = 'A';
				
				EXEC dbo.ams_getMemberDataByFieldSets @orgID=@orgID, @fieldsetIDList="#valueList(local.fieldSets.fieldsetid)#", 
				@existingFields='m_prefix,m_firstname,m_middlename,m_lastname,m_suffix,m_professionalsuffix,m_membernumber,m_company', @ovNameFormat=NULL, 
				@ovMaskEmails=NULL, @membersTableName='##tmpMembers', @membersResultTableName='##tmpMembersFS', 
				@linkedMembers=0, @mode='view', @outputFieldsXML=@outputFieldsXML OUTPUT;
				
				SELECT  m.firstname, m.middlename, m.lastname, m.memberNumber, m.company, m.prefix, m.suffix, m.professionalsuffix, tmpM.*, @totalCount as totalCount, m.row, CASE WHEN m.row = (@posStart+1) THEN @outputFieldsXML ELSE NULL END AS mc_outputFieldsXML
				FROM ##tmpMembers AS m 
				INNER JOIN ##tmpMembersFS AS tmpM ON tmpM.memberID = m.memberID
				WHERE m.row > @posStart
				AND m.row <= @posStartAndCount
				ORDER BY m.row;
				
				IF OBJECT_ID('tempdb..##tblMemHistorySearch') IS NOT NULL
					DROP TABLE ##tblMemHistorySearch;
				IF OBJECT_ID('tempdb..##tblMemUnmatch') IS NOT NULL
					DROP TABLE ##tblMemUnmatch;
				IF OBJECT_ID('tempdb..##tblMemUnmatchLinked') IS NOT NULL
					DROP TABLE ##tblMemUnmatchLinked;				
				IF OBJECT_ID('tempdb..##tmpMembers') IS NOT NULL 
					DROP TABLE ##tmpMembers;
				IF OBJECT_ID('tempdb..##tmpMembersFS') IS NOT NULL
					DROP TABLE ##tmpMembersFS;

				SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
			END TRY
			BEGIN CATCH
				IF @@trancount > 0 ROLLBACK TRANSACTION;
				SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
				EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
			END CATCH	
		</cfquery>
		
		<cfreturn local.qryMemHistory>
	</cffunction>

	<cffunction name="generateMatch" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>

		<cfsetting requesttimeout="300">

		<cfset local.siteID = arguments.event.getValue('mc_siteinfo.siteid')>
		<cfset local.dateFrom = arguments.event.getTrimValue('fDate','')>
		<cfif len(local.dateFrom)>
			<cfset local.dateFrom = createDateTime(year(local.dateFrom), month(local.dateFrom), 1, 00, 00, 00)>
		</cfif>
		<cfset local.dateTo = arguments.event.getTrimValue('fDate','')>
		<cfif len(local.dateTo)>
			<cfset local.dateTo = createDateTime(year(local.dateTo), month(local.dateTo), daysInMonth(local.dateTo), 23, 59, 59.997)>
		</cfif>

		<cfset local.objMemberHistory = CreateObject('component','model.admin.memberHistory.memberHistory')>
		<cfset local.RelationshipAdminSRID = application.objSiteResource.getSiteResourceIDForResourceType(resourceTypeName='RelationshipAdmin',siteID=local.siteID)>
		<cfset local.qryCategories = local.objMemberHistory.getParentChildCategories(siteID=local.siteID, siteResourceID=local.RelationshipAdminSRID, checkPermission='AddEntry')>
		<cfquery name="local.qryParentCategory" dbtype="query">
			select categoryID from [local].qryCategories where categoryCode = 'Network' and parentCategoryID = 0
		</cfquery>
		<cfset local.sCategoryID = val(local.qryParentCategory.categoryID)>
		<cfquery name="local.qryChildCategory" dbtype="query">
			select categoryID from [local].qryCategories where categoryCode = 'Matched' and parentCategoryID = <cfqueryparam value="#local.sCategoryID#" cfsqltype="cf_sql_integer">
		</cfquery>
		<cfset local.sSubCategoryID = val(local.qryChildCategory.categoryID)>

		<cfstoredproc datasource="#application.dsn.customApps.dsn#" procedure="indy_filterMatchSettings">
			<cfprocparam type="in" cfsqltype="cf_sql_timestamp" value="#local.dateFrom#">
			<cfprocparam type="in" cfsqltype="cf_sql_timestamp" value="#local.dateTo#">
			<cfprocparam type="in" cfsqltype="cf_sql_varchar" value="#arguments.event.getValue('mc_siteinfo.orgcode')#">
			<cfprocresult name="local.qryfilterDataUnMatched" resultset="1">
			<cfprocresult name="local.qryfilterDataMatched" resultset="2">
			<cfprocresult name="local.qryfilterDataUnMatchedCount" resultset="3">
			<cfprocresult name="local.qryfilterDataMatchedCount" resultset="4">
		</cfstoredproc>

		<cfset local.success = 0>
		<cfif local.qryfilterDataMatched.recordCount>
			<cftry>
				<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryCreateMatches">
					SET XACT_ABORT, NOCOUNT ON;
					BEGIN TRY				

						declare @historyID int;
						declare 
							@categoryID int = <cfqueryparam value="#local.sCategoryID#" cfsqltype="cf_sql_integer">, 
							@subCategoryID int = <cfqueryparam value="#local.sSubCategoryID#" cfsqltype="cf_sql_integer">, 
							@userDate datetime = <cfqueryparam value="#local.dateFrom#" cfsqltype="cf_sql_timestamp">,
							@userEndDate datetime = <cfqueryparam value="#local.dateTo#" cfsqltype="cf_sql_timestamp">,
							@enteredByMemberID int = <cfqueryparam value="#session.cfcuser.memberdata.memberid#" cfsqltype="cf_sql_integer">

						<cfloop query="local.qryfilterDataMatched">
							exec dbo.ams_addMemberHistory
								@typeID = 2,
								@memberID = <cfqueryparam value="#local.qryfilterDataMatched.memberID#" cfsqltype="cf_sql_integer">,
								@categoryID = @categoryID, 
								@subCategoryID = @subCategoryID,
								@userDate = @userDate,
								@userEndDate = @userEndDate,
								@qty = NULL,
								@dollarAmt = NULL,
								@description = '',
								@linkMemberID = <cfqueryparam value="#local.qryfilterDataMatched.linkedMemberID#" cfsqltype="cf_sql_integer">,
								@usageCSRID = NULL,
								@usageResourceType = NULL,
								@usageType = NULL,
								@usageItemID = NULL,
								@enteredByMemberID = @enteredByMemberID,
								@historyID = @historyID OUTPUT;			
						</cfloop>

						select 1 as success;

					END TRY
					BEGIN CATCH
						IF @@trancount > 0 ROLLBACK TRANSACTION;
						select 0 as success;
						EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
					END CATCH
				</cfquery>
				<cfcatch type="database">
					<cfset application.objError.sendError(cfcatch=cfcatch,objectToDump=local)>
				</cfcatch>
			</cftry>
		</cfif>

		<cfif structKeyExists(local, "qryCreateMatches")>
			<cfset local.success = local.qryCreateMatches.success>
		</cfif>
		
		<cfsavecontent variable="local.data">
			<cfoutput>
				<cfif local.qryfilterDataMatched.recordCount and local.success>
					#local.qryfilterDataMatched.recordCount# match(es) generated!
				<cfelseif not local.qryfilterDataMatched.recordCount>
					No matches generated.
				<cfelse>
					Match generation failed!
				</cfif>
			</cfoutput>
		</cfsavecontent>
		
		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="prepareMemberList" access="public" returntype="struct" output="false">
		<cfargument name="qryfilterData" type="query" required="true">
		<cfargument name="orgID" type="numeric" required="yes">
		
		<cfscript>
			var local = structNew();
			local.returnStruct = structNew();
			local.returnStruct.success = true;
			local.returnStruct.data = arrayNew(1);
			local.qryMembers = arguments.qryfilterData;
			local.returnStruct.totalCount = local.qryMembers.recordcount;
		</cfscript>
		
		<cfif local.qryMembers.recordcount gt 0>
			<cfset local.xmlResultFields = local.qryMembers.mc_outputFieldsXML[1]>
			<cfset local.qryOutputFields = CreateObject("component","model.system.platform.memberFieldsets").getOutputFieldsFromXML(outputFieldsXML=local.xmlResultFields)>

			<cfset local.orgAddressTypes = application.objOrgInfo.getOrgAddressTypes(orgID=arguments.orgID, includeTags=1)>
			<cfset local.strOrgAddressTypes = structNew()>
			<cfloop query="local.orgAddressTypes">
				<cfif local.orgAddressTypes.isTag is 1>
					<cfset local.strOrgAddressTypes["t#local.orgAddressTypes.addressTypeID#"] = local.orgAddressTypes.addressType>
				<cfelse>
					<cfset local.strOrgAddressTypes[local.orgAddressTypes.addressTypeID] = local.orgAddressTypes.addressType>
				</cfif>
			</cfloop>
			<cfset local.mc_combinedAddresses = structNew()>
			<cfset local.tmp = XMLSearch(local.xmlResultFields,"//field[substring(@fieldCode,1,3)='ma_' or substring(@fieldCode,1,3)='mp_' or substring(@fieldCode,1,4)='mat_' or substring(@fieldCode,1,4)='mpt_']")>
			<cfloop array="#local.tmp#" index="local.thisField">
				<cfset local.thisATID = listGetAt(local.thisField.xmlattributes.fieldcode,2,"_")>
				<cfif listFindNoCase("mat,mpt",listGetAt(local.thisField.xmlattributes.fieldcode,1,"_"))>
					<cfset local.strKey = "t#local.thisATID#">
				<cfelse>
					<cfset local.strKey = local.thisATID>
				</cfif>
				<cfif NOT StructKeyExists(local.mc_combinedAddresses,local.strKey)>
					<cfset local.mc_combinedAddresses[local.strKey] = { addr='', type=local.strOrgAddressTypes[local.strKey] } >
				</cfif>
			</cfloop>

			<!--- remove fields from qryOutputFields that are handled manually --->
			<cfquery name="local.qryOutputFieldsForLoop" dbtype="query">
				SELECT *
				FROM [local].qryOutputFields
				WHERE fieldcodeSect NOT IN ('mc','m','ma','mat','mp','mpt')
			</cfquery>
			
			<cfloop query="local.qryMembers">
				<cfset local.mc_combinedName = local.qryMembers['Extended MemberNumber'][local.qryMembers.currentrow]>

				<!--- combine address fields if there are any --->
				<cfset local.thisMem_mc_combinedAddresses = duplicate(local.mc_combinedAddresses)>
				<cfloop collection="#local.thisMem_mc_combinedAddresses#" item="local.thisATID">
					<cfsavecontent variable="local.thisATFull">
						<cfoutput>
						<cfif left(local.thisATID,1) eq "t">
							<cfset local.MAfcPrefix = "mat_#right(local.thisATID,len(local.thisATID)-1)#_">
							<cfset local.MPfcPrefix = "mpt_#right(local.thisATID,len(local.thisATID)-1)#_">
						<cfelse>
							<cfset local.MAfcPrefix = "ma_#local.thisATID#_">
							<cfset local.MPfcPrefix = "mp_#local.thisATID#_">
						</cfif>

						<cfset local.tmp = XMLSearch(local.xmlResultFields,"//field[@fieldCode='#local.MAfcPrefix#address1']")>
						<cfif arrayLen(local.tmp) is 1 and len(local.qryMembers[local.tmp[1].xmlAttributes.FieldLabel][local.qryMembers.currentrow])>#encodeForHTML(local.qryMembers[local.tmp[1].xmlAttributes.FieldLabel][local.qryMembers.currentrow])#<br/> </cfif>
						<cfset local.tmp = XMLSearch(local.xmlResultFields,"//field[@fieldCode='#local.MAfcPrefix#address2']")>
						<cfif arrayLen(local.tmp) is 1 and len(local.qryMembers[local.tmp[1].xmlAttributes.FieldLabel][local.qryMembers.currentrow])>#encodeForHTML(local.qryMembers[local.tmp[1].xmlAttributes.FieldLabel][local.qryMembers.currentrow])#<br/> </cfif>
						<cfset local.tmp = XMLSearch(local.xmlResultFields,"//field[@fieldCode='#local.MAfcPrefix#address3']")>
						<cfif arrayLen(local.tmp) is 1 and len(local.qryMembers[local.tmp[1].xmlAttributes.FieldLabel][local.qryMembers.currentrow])>#encodeForHTML(local.qryMembers[local.tmp[1].xmlAttributes.FieldLabel][local.qryMembers.currentrow])#<br/></cfif>
						<cfset local.tmp = XMLSearch(local.xmlResultFields,"//field[@fieldCode='#local.MAfcPrefix#city']")>
						<cfif arrayLen(local.tmp) is 1 and len(local.qryMembers[local.tmp[1].xmlAttributes.FieldLabel][local.qryMembers.currentrow])>#encodeForHTML(local.qryMembers[local.tmp[1].xmlAttributes.FieldLabel][local.qryMembers.currentrow])# </cfif>
						<cfset local.tmp2 = XMLSearch(local.xmlResultFields,"//field[@fieldCode='#local.MAfcPrefix#stateprov']")>
						<cfif arrayLen(local.tmp2) is 1 and len(local.qryMembers[local.tmp2[1].xmlAttributes.FieldLabel][local.qryMembers.currentrow])>, #encodeForHTML(local.qryMembers[local.tmp2[1].xmlAttributes.FieldLabel][local.qryMembers.currentrow])# </cfif>
						<cfset local.tmp = XMLSearch(local.xmlResultFields,"//field[@fieldCode='#local.MAfcPrefix#postalcode']")>
						<cfif arrayLen(local.tmp) is 1 and len(local.qryMembers[local.tmp[1].xmlAttributes.FieldLabel][local.qryMembers.currentrow])> #encodeForHTML(local.qryMembers[local.tmp[1].xmlAttributes.FieldLabel][local.qryMembers.currentrow])#<br/></cfif>
						<cfset local.tmp = XMLSearch(local.xmlResultFields,"//field[@fieldCode='#local.MAfcPrefix#county']")>
						<cfif arrayLen(local.tmp) is 1 and len(local.qryMembers[local.tmp[1].xmlAttributes.FieldLabel][local.qryMembers.currentrow])>#encodeForHTML(local.qryMembers[local.tmp[1].xmlAttributes.FieldLabel][local.qryMembers.currentrow])# County<br/></cfif>
						<cfset local.tmp = XMLSearch(local.xmlResultFields,"//field[@fieldCode='#local.MAfcPrefix#country']")>
						<cfif arrayLen(local.tmp) is 1 and len(local.qryMembers[local.tmp[1].xmlAttributes.FieldLabel][local.qryMembers.currentrow])> #encodeForHTML(local.qryMembers[local.tmp[1].xmlAttributes.FieldLabel][local.qryMembers.currentrow])#<br/></cfif>
						<cfset local.tmp = XMLSearch(local.xmlResultFields,"//field[substring(@fieldCode,1,#len(local.MPfcPrefix)#)='#local.MPfcPrefix#']")>
						<cfloop array="#local.tmp#" index="local.thisPT">
							<cfif len(local.qryMembers[local.thisPT.xmlAttributes.FieldLabel][local.qryMembers.currentrow])>
								<div>#local.thisPT.xmlAttributes.FieldLabel#: #encodeForHTML(local.qryMembers[local.thisPT.xmlAttributes.FieldLabel][local.qryMembers.currentrow])#</div>
							</cfif>
						</cfloop>
						</cfoutput>
					</cfsavecontent>
					<cfset local.thisATfull = trim(replace(replace(rereplace(local.thisATFull,'[\r\n\t]','','ALL'),'  ',' ','ALL'),' ,',',','ALL'))>
					<cfif left(local.thisATfull,2) eq ", ">
						<cfset local.thisATfull = right(local.thisATfull,len(local.thisATfull)-2)>
					</cfif>
					<cfif len(local.thisATfull)>
						<cfset local.thisMem_mc_combinedAddresses[local.thisATID]['addr'] = local.thisATfull>
					<cfelse>
						<cfset structDelete(local.thisMem_mc_combinedAddresses,local.thisATID,false)>
					</cfif>
				</cfloop>
				<cfset local.memberLink = buildLinkToTool(toolType='MemberAdmin',mca_ta='edit')& "&memberID="&local.qryMembers.memberid>
				
				<cfsavecontent variable="local.data">
					<cfoutput>
					<a class="memberLink" href="#local.memberLink#" target="_blank">#encodeForHTML(local.mc_combinedName)#</a>
					<div class="small text-dim">#encodeForHTML(local.qryMembers.company)#</div>
					<cfif StructCount(local.thisMem_mc_combinedAddresses)>
						<cfloop collection="#local.thisMem_mc_combinedAddresses#" item="local.thisATID">
							<div><b style="font-size:95%; margin-right:15px;">#local.thisMem_mc_combinedAddresses[local.thisATID]['type']#</b><br/>#local.thisMem_mc_combinedAddresses[local.thisATID]['addr']#</div>
						</cfloop>
					</cfif>
					<cfif local.qryOutputFieldsForLoop.recordCount>
						<cfloop query="local.qryOutputFieldsForLoop">
							<cfset local.currValue = local.qryMembers[local.qryOutputFieldsForLoop.fieldLabel][local.qryMembers.currentrow]>
							<cfif len(local.currValue)>
								<div>
									#htmlEditFormat(local.qryOutputFieldsForLoop.fieldLabel)#: 
									<cfif left(local.qryOutputFieldsForLoop.fieldCode,13) eq 'acct_balance_'>
										#dollarFormat(local.currValue)#
									<cfelseif left(local.qryOutputFieldsForLoop.fieldCode,3) eq 'me_'>
										<a href="mailto:#local.currValue#">#local.currValue#</a>
									<cfelseif left(local.qryOutputFieldsForLoop.fieldCode,3) eq 'mw_'>
										<a href="#local.currValue#" target="_blank">#local.currValue#</a>
									<cfelse>
										<cfswitch expression="#local.qryOutputFieldsForLoop.dataTypeCode#">
											<cfcase value="DATE">
												#dateFormat(local.currValue,"m/d/yyyy")#
											</cfcase>
											<cfcase value="STRING,DECIMAL2,INTEGER">
												#local.currValue#
											</cfcase>
											<cfcase value="BIT">
												#YesNoFormat(local.currValue)#
											</cfcase>
										</cfswitch>
									</cfif>
								</div>
							</cfif>
						</cfloop>
					</cfif>
					</cfoutput>
				</cfsavecontent>
				<cfset arrayAppend(local.returnStruct.data, application.objCommon.minText(local.data))>
			</cfloop>
		</cfif>
		<cfreturn returnAppStruct(local.returnStruct.data,"echo")>
	</cffunction>
</cfcomponent>